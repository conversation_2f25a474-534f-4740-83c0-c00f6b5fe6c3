package com.vcc.bigdata.presentation.controller.audience;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vcc.bigdata.application.dto.AudienceDto;
import com.vcc.bigdata.domain.service.IAudienceService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(AudienceController.class)
@DisplayName("AudienceController Integration Tests")
class AudienceControllerIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private IAudienceService audienceService;

    @Autowired
    private ObjectMapper objectMapper;

    private static final String USER_ID = "integration-test-user";
    private static final String AUDIENCE_ID = "123";

    // ========== Edge Cases and Error Scenarios ==========

    @Test
    @DisplayName("Should handle malformed JSON in request body")
    void createAudience_WithMalformedJson_ShouldReturnBadRequest() throws Exception {
        mockMvc.perform(post("/v1/audiences")
                .header("user-id", USER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content("{invalid json"))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("Should handle very long user ID")
    void getAudiences_WithVeryLongUserId_ShouldReturnSuccess() throws Exception {
        // Given
        String longUserId = "a".repeat(1000);
        when(audienceService.getAll(anyLong(), anyInt(), anyString(), anyInt(), anyInt(), 
                isNull(), isNull(), isNull(), isNull(), anyInt()))
                .thenReturn(null);

        // When & Then
        mockMvc.perform(get("/v1/audiences")
                .header("user-id", longUserId))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("Should handle special characters in search parameter")
    void getAudiences_WithSpecialCharactersInSearch_ShouldReturnSuccess() throws Exception {
        // Given
        String searchWithSpecialChars = "test@#$%^&*()_+{}|:<>?[];',./";
        when(audienceService.getAll(anyLong(), anyInt(), anyString(), anyInt(), anyInt(), 
                eq(searchWithSpecialChars), isNull(), isNull(), isNull(), anyInt()))
                .thenReturn(null);

        // When & Then
        mockMvc.perform(get("/v1/audiences")
                .header("user-id", USER_ID)
                .param("search", searchWithSpecialChars))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("Should handle negative page and limit values")
    void getAudiences_WithNegativePageAndLimit_ShouldReturnSuccess() throws Exception {
        // Given
        when(audienceService.getAll(anyLong(), anyInt(), anyString(), eq(-1), eq(-5), 
                isNull(), isNull(), isNull(), isNull(), anyInt()))
                .thenReturn(null);

        // When & Then
        mockMvc.perform(get("/v1/audiences")
                .header("user-id", USER_ID)
                .param("page", "-1")
                .param("limit", "-5"))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("Should handle zero ID in path parameter")
    void getAudience_WithZeroId_ShouldReturnSuccess() throws Exception {
        // Given
        when(audienceService.getAudience(anyLong(), eq(0L))).thenReturn(new AudienceDto());

        // When & Then
        mockMvc.perform(get("/v1/audiences/{id}", "0")
                .header("user-id", USER_ID))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("Should handle very large ID in path parameter")
    void getAudience_WithVeryLargeId_ShouldReturnSuccess() throws Exception {
        // Given
        String largeId = String.valueOf(Long.MAX_VALUE);
        when(audienceService.getAudience(anyLong(), eq(Long.MAX_VALUE))).thenReturn(new AudienceDto());

        // When & Then
        mockMvc.perform(get("/v1/audiences/{id}", largeId)
                .header("user-id", USER_ID))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("Should handle empty audience name in check-name endpoint")
    void checkName_WithEmptyName_ShouldReturnSuccess() throws Exception {
        // Given
        when(audienceService.checkNameExists(anyLong(), eq(""))).thenReturn(false);

        // When & Then
        mockMvc.perform(get("/v1/audiences/check-name")
                .header("user-id", USER_ID)
                .param("name", ""))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("Should handle audience name with only whitespace")
    void checkName_WithWhitespaceOnlyName_ShouldReturnSuccess() throws Exception {
        // Given
        String whitespaceName = "   \t\n   ";
        when(audienceService.checkNameExists(anyLong(), eq(whitespaceName))).thenReturn(false);

        // When & Then
        mockMvc.perform(get("/v1/audiences/check-name")
                .header("user-id", USER_ID)
                .param("name", whitespaceName))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("Should handle very long audience name")
    void checkName_WithVeryLongName_ShouldReturnSuccess() throws Exception {
        // Given
        String longName = "a".repeat(10000);
        when(audienceService.checkNameExists(anyLong(), eq(longName))).thenReturn(false);

        // When & Then
        mockMvc.perform(get("/v1/audiences/check-name")
                .header("user-id", USER_ID)
                .param("name", longName))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("Should handle audience DTO with null fields")
    void createAudience_WithNullFields_ShouldReturnSuccess() throws Exception {
        // Given
        AudienceDto audienceWithNulls = AudienceDto.builder()
                .name(null)
                .description(null)
                .segmentRule(null)
                .build();
        
        AudienceDto createdAudience = AudienceDto.builder()
                .id("1")
                .name("Created Audience")
                .build();
        
        when(audienceService.createAudience(any(AudienceDto.class))).thenReturn(createdAudience);

        // When & Then
        mockMvc.perform(post("/v1/audiences")
                .header("user-id", USER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(audienceWithNulls)))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("Should handle audience DTO with complex segment rule")
    void createAudience_WithComplexSegmentRule_ShouldReturnSuccess() throws Exception {
        // Given
        AudienceDto.Rule rule1 = new AudienceDto.Rule("age", "number", "gte", 18);
        AudienceDto.Rule rule2 = new AudienceDto.Rule("gender", "string", "eq", "male");
        
        AudienceDto.Group group = new AudienceDto.Group();
        group.setRuleExpression("rule1 AND rule2");
        group.setRules(Arrays.asList(rule1, rule2));
        
        AudienceDto.SegmentRule segmentRule = new AudienceDto.SegmentRule();
        segmentRule.setGroupExpression("group1");
        segmentRule.setGroups(Arrays.asList(group));
        
        AudienceDto complexAudience = AudienceDto.builder()
                .name("Complex Audience")
                .description("Audience with complex rules")
                .segmentRule(segmentRule)
                .type(1)
                .build();
        
        AudienceDto createdAudience = AudienceDto.builder()
                .id("1")
                .name("Complex Audience")
                .segmentRule(segmentRule)
                .build();
        
        when(audienceService.createAudience(any(AudienceDto.class))).thenReturn(createdAudience);

        // When & Then
        mockMvc.perform(post("/v1/audiences")
                .header("user-id", USER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(complexAudience)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.segmentRule").exists())
                .andExpect(jsonPath("$.data.segmentRule.groups[0].rules").isArray())
                .andExpect(jsonPath("$.data.segmentRule.groups[0].rules").isNotEmpty());
    }

    @Test
    @DisplayName("Should handle invalid date formats in date parameters")
    void getAudiences_WithInvalidDateFormats_ShouldReturnSuccess() throws Exception {
        // Given
        when(audienceService.getAll(anyLong(), anyInt(), anyString(), anyInt(), anyInt(), 
                isNull(), eq("invalid-date"), eq("also-invalid"), eq("created_at"), anyInt()))
                .thenReturn(null);

        // When & Then
        mockMvc.perform(get("/v1/audiences")
                .header("user-id", USER_ID)
                .param("from_date", "invalid-date")
                .param("to_date", "also-invalid")
                .param("date_field", "created_at"))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("Should handle concurrent requests to same endpoint")
    void getAudiences_ConcurrentRequests_ShouldHandleGracefully() throws Exception {
        // Given
        when(audienceService.getAll(anyLong(), anyInt(), anyString(), anyInt(), anyInt(), 
                isNull(), isNull(), isNull(), isNull(), anyInt()))
                .thenReturn(null);

        // When & Then - Simulate concurrent requests
        for (int i = 0; i < 10; i++) {
            mockMvc.perform(get("/v1/audiences")
                    .header("user-id", USER_ID + "-" + i))
                    .andExpect(status().isOk());
        }
    }

    @Test
    @DisplayName("Should handle maximum limit boundary correctly")
    void getAudiences_WithExactMaxLimit_ShouldReturnSuccess() throws Exception {
        // Given
        when(audienceService.getAll(anyLong(), anyInt(), anyString(), anyInt(), eq(100), 
                isNull(), isNull(), isNull(), isNull(), anyInt()))
                .thenReturn(null);

        // When & Then
        mockMvc.perform(get("/v1/audiences")
                .header("user-id", USER_ID)
                .param("limit", "100"))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("Should handle limit just above maximum")
    void getAudiences_WithLimitJustAboveMax_ShouldLimitTo100() throws Exception {
        // Given
        when(audienceService.getAll(anyLong(), anyInt(), anyString(), anyInt(), eq(100), 
                isNull(), isNull(), isNull(), isNull(), anyInt()))
                .thenReturn(null);

        // When & Then
        mockMvc.perform(get("/v1/audiences")
                .header("user-id", USER_ID)
                .param("limit", "101"))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("Should handle all sort options")
    void getAudiences_WithDifferentSortOptions_ShouldReturnSuccess() throws Exception {
        // Given
        List<String> sortOptions = Arrays.asList(
                "created_at:asc", "created_at:desc",
                "updated_at:asc", "updated_at:desc",
                "name:asc", "name:desc"
        );

        for (String sort : sortOptions) {
            when(audienceService.getAll(anyLong(), anyInt(), eq(sort), anyInt(), anyInt(), 
                    isNull(), isNull(), isNull(), isNull(), anyInt()))
                    .thenReturn(null);

            // When & Then
            mockMvc.perform(get("/v1/audiences")
                    .header("user-id", USER_ID)
                    .param("sort", sort))
                    .andExpect(status().isOk());
        }
    }
}
