# AudienceController Test Cases

This directory contains comprehensive test cases for the `AudienceController` class, covering all 10 endpoints with various scenarios.

## Test Files Overview

### 1. `AudienceControllerTest.java`
**Unit tests with mocked dependencies**
- Tests controller logic in isolation
- Uses `@ExtendWith(MockitoExtension.class)`
- Mocks `IAudienceService` dependency
- Focuses on controller behavior and parameter handling

### 2. `AudienceControllerIntegrationTest.java`
**Integration tests with Spring context**
- Tests with Spring Web MVC context
- Uses `@WebMvcTest(AudienceController.class)`
- Tests edge cases and error scenarios
- Validates request/response handling

### 3. `AudienceControllerTestSuite.java`
**Test suite runner**
- Runs all AudienceController tests together
- Use: `mvn test -Dtest=AudienceControllerTestSuite`

## Endpoints Tested

| HTTP Method | Endpoint | Test Scenarios |
|-------------|----------|----------------|
| GET | `/v1/audiences` | Default params, custom params, limit validation, missing headers |
| GET | `/v1/audiences/{id}` | Valid ID, invalid ID format, missing headers |
| POST | `/v1/audiences` | Valid data, missing headers, empty body, malformed JSON |
| PUT | `/v1/audiences/{id}` | Valid update, missing headers, invalid data |
| DELETE | `/v1/audiences/{id}` | Valid deletion, missing headers, invalid ID |
| POST | `/v1/audiences/estimate` | Valid estimation, missing headers, invalid data |
| POST | `/v1/audiences/preview-profiles` | Valid preview, missing headers, invalid data |
| GET | `/v1/audiences/{id}/profiles` | Default params, custom params, limit validation |
| GET | `/v1/audiences/check-name` | Valid name, empty name, missing param, special chars |
| POST | `/v1/audiences/total-profiles` | Valid IDs, empty list, missing headers |

## Test Categories

### 1. Happy Path Tests ✅
- Valid requests with expected responses
- Default parameter handling
- Successful CRUD operations

### 2. Validation Tests ⚠️
- Missing required headers (`user-id`)
- Missing required parameters
- Invalid parameter formats
- Boundary value testing (limits, IDs)

### 3. Edge Cases 🔍
- Empty request bodies
- Null values in DTOs
- Special characters in parameters
- Very long strings
- Negative values
- Maximum boundary values

### 4. Error Scenarios ❌
- Malformed JSON
- Invalid ID formats
- Missing request bodies
- Service layer exceptions

## Key Test Features

### Data Builders
```java
private AudienceDto createSampleAudienceDto() {
    return AudienceDto.builder()
            .id(AUDIENCE_ID)
            .name("Test Audience")
            .description("Test Description")
            .userId(USER_ID)
            .type(0)
            .profileCount(100L)
            .build();
}
```

### Parameterized Testing
- Tests multiple sort options
- Tests various limit values
- Tests different parameter combinations

### Mock Verification
```java
verify(audienceService).getAll(HASHED_USER_ID, 0, "created_at:asc", 0, 10, null, null, null, null, 0);
```

### JSON Path Assertions
```java
.andExpect(jsonPath("$.message").value("Get success"))
.andExpect(jsonPath("$.data.id").value(AUDIENCE_ID))
.andExpect(jsonPath("$.data.name").value("Test Audience"))
```

## Running the Tests

### Run All Tests
```bash
mvn test -Dtest=AudienceController*
```

### Run Specific Test Class
```bash
mvn test -Dtest=AudienceControllerTest
mvn test -Dtest=AudienceControllerIntegrationTest
```

### Run Test Suite
```bash
mvn test -Dtest=AudienceControllerTestSuite
```

### Run with Coverage
```bash
mvn test jacoco:report
```

## Test Coverage

The test suite covers:
- ✅ All 10 controller endpoints
- ✅ All HTTP methods (GET, POST, PUT, DELETE)
- ✅ Parameter validation
- ✅ Header validation
- ✅ Request body validation
- ✅ Response structure validation
- ✅ Error handling
- ✅ Edge cases
- ✅ Boundary conditions

## Dependencies Required

```xml
<dependencies>
    <!-- Spring Boot Test Starter -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <scope>test</scope>
    </dependency>
    
    <!-- JUnit 5 -->
    <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter</artifactId>
        <scope>test</scope>
    </dependency>
    
    <!-- Mockito -->
    <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <scope>test</scope>
    </dependency>
</dependencies>
```

## Best Practices Implemented

1. **Clear Test Names**: Descriptive method names following `methodName_condition_expectedResult` pattern
2. **Arrange-Act-Assert**: Clear test structure with Given-When-Then comments
3. **Test Isolation**: Each test is independent and can run in any order
4. **Mock Verification**: Verifies service method calls with correct parameters
5. **Comprehensive Coverage**: Tests both success and failure scenarios
6. **Edge Case Testing**: Handles boundary conditions and unusual inputs
7. **Documentation**: Clear comments and documentation for maintainability

## Future Enhancements

- Add performance tests for high-load scenarios
- Add security tests for authentication/authorization
- Add contract tests for API compatibility
- Add database integration tests
- Add end-to-end tests with real data
