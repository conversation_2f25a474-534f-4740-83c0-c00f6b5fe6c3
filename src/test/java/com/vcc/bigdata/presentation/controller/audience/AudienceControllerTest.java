package com.vcc.bigdata.presentation.controller.audience;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vcc.bigdata.application.dto.AudienceDto;
import com.vcc.bigdata.application.dto.ProfilesResponseDto;
import com.vcc.bigdata.domain.model.AudienceResult;
import com.vcc.bigdata.domain.service.IAudienceService;
import com.vcc.bigdata.presentation.response.audience.AudienceResponse;
import com.vcc.bigdata.utility.Hashings;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.doNothing;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("AudienceController Tests")
class AudienceControllerTest {

    @Mock
    private IAudienceService audienceService;

    @InjectMocks
    private AudienceController audienceController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    private static final String USER_ID = "test-user-123";
    private static final String AUDIENCE_ID = "1";
    private static final Long HASHED_USER_ID = Hashings.CRC32(USER_ID);

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(audienceController).build();
        objectMapper = new ObjectMapper();
    }

    // Test data builders
    private AudienceDto createSampleAudienceDto() {
        return AudienceDto.builder()
                .id(AUDIENCE_ID)
                .name("Test Audience")
                .description("Test Description")
                .userId(USER_ID)
                .type(0)
                .profileCount(100L)
                .createdAt(new Timestamp(System.currentTimeMillis()))
                .updatedAt(new Timestamp(System.currentTimeMillis()))
                .build();
    }

    private AudienceResponse<List<AudienceDto>> createSampleAudienceListResponse() {
        List<AudienceDto> audiences = Arrays.asList(createSampleAudienceDto());
        return new AudienceResponse<>(audiences);
    }

    // ========== GET /v1/audiences Tests ==========

    @Test
    @DisplayName("GET /v1/audiences - Should return audiences with default parameters")
    void getAudiences_WithDefaultParameters_ShouldReturnSuccess() throws Exception {
        // Given
        AudienceResponse<List<AudienceDto>> expectedResponse = createSampleAudienceListResponse();
        when(audienceService.getAll(eq(HASHED_USER_ID), eq(0), eq("created_at:asc"),
                eq(0), eq(10), isNull(), isNull(), isNull(), isNull(), eq(0)))
                .thenReturn(expectedResponse);

        // When & Then
        mockMvc.perform(get("/v1/audiences")
                .header("user-id", USER_ID))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data[0].id").value(AUDIENCE_ID))
                .andExpect(jsonPath("$.data[0].name").value("Test Audience"));

        verify(audienceService).getAll(HASHED_USER_ID, 0, "created_at:asc", 0, 10, null, null, null, null, 0);
    }

    @Test
    @DisplayName("GET /v1/audiences - Should return audiences with custom parameters")
    void getAudiences_WithCustomParameters_ShouldReturnSuccess() throws Exception {
        // Given
        AudienceResponse<List<AudienceDto>> expectedResponse = createSampleAudienceListResponse();
        when(audienceService.getAll(eq(HASHED_USER_ID), eq(1), eq("name:desc"),
                eq(1), eq(20), eq("test"), eq("2023-01-01"), eq("2023-12-31"), eq("created_at"), eq(1)))
                .thenReturn(expectedResponse);

        // When & Then
        mockMvc.perform(get("/v1/audiences")
                .header("user-id", USER_ID)
                .param("shared", "1")
                .param("sort", "name:desc")
                .param("search", "test")
                .param("page", "1")
                .param("limit", "20")
                .param("from_date", "2023-01-01")
                .param("to_date", "2023-12-31")
                .param("date_field", "created_at")
                .param("type", "1"))
                .andExpect(status().isOk());

        verify(audienceService).getAll(HASHED_USER_ID, 1, "name:desc", 1, 20, "test", "2023-01-01", "2023-12-31", "created_at", 1);
    }

    @Test
    @DisplayName("GET /v1/audiences - Should limit to 100 when limit exceeds maximum")
    void getAudiences_WithLimitExceedsMaximum_ShouldLimitTo100() throws Exception {
        // Given
        AudienceResponse<List<AudienceDto>> expectedResponse = createSampleAudienceListResponse();
        when(audienceService.getAll(eq(HASHED_USER_ID), eq(0), eq("created_at:asc"),
                eq(0), eq(100), isNull(), isNull(), isNull(), isNull(), eq(0)))
                .thenReturn(expectedResponse);

        // When & Then
        mockMvc.perform(get("/v1/audiences")
                .header("user-id", USER_ID)
                .param("limit", "150"))
                .andExpect(status().isOk());

        verify(audienceService).getAll(HASHED_USER_ID, 0, "created_at:asc", 0, 100, null, null, null, null, 0);
    }

    @Test
    @DisplayName("GET /v1/audiences - Should return 400 when user-id header is missing")
    void getAudiences_WithoutUserIdHeader_ShouldReturnBadRequest() throws Exception {
        // When & Then
        mockMvc.perform(get("/v1/audiences"))
                .andExpect(status().isBadRequest());
    }

    // ========== GET /v1/audiences/{id} Tests ==========

    @Test
    @DisplayName("GET /v1/audiences/{id} - Should return specific audience")
    void getAudience_WithValidId_ShouldReturnSuccess() throws Exception {
        // Given
        AudienceDto expectedAudience = createSampleAudienceDto();
        when(audienceService.getAudience(HASHED_USER_ID, 1L)).thenReturn(expectedAudience);

        // When & Then
        mockMvc.perform(get("/v1/audiences/{id}", AUDIENCE_ID)
                .header("user-id", USER_ID))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Get success"))
                .andExpect(jsonPath("$.data.id").value(AUDIENCE_ID))
                .andExpect(jsonPath("$.data.name").value("Test Audience"));

        verify(audienceService).getAudience(HASHED_USER_ID, 1L);
    }

    @Test
    @DisplayName("GET /v1/audiences/{id} - Should return 400 when user-id header is missing")
    void getAudience_WithoutUserIdHeader_ShouldReturnBadRequest() throws Exception {
        // When & Then
        mockMvc.perform(get("/v1/audiences/{id}", AUDIENCE_ID))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("GET /v1/audiences/{id} - Should handle invalid ID format")
    void getAudience_WithInvalidIdFormat_ShouldReturnBadRequest() throws Exception {
        // When & Then
        mockMvc.perform(get("/v1/audiences/{id}", "invalid-id")
                .header("user-id", USER_ID))
                .andExpect(status().isBadRequest());
    }

    // ========== POST /v1/audiences Tests ==========

    @Test
    @DisplayName("POST /v1/audiences - Should create new audience successfully")
    void createAudience_WithValidData_ShouldReturnSuccess() throws Exception {
        // Given
        AudienceDto inputAudience = AudienceDto.builder()
                .name("New Audience")
                .description("New Description")
                .type(0)
                .build();

        AudienceDto createdAudience = createSampleAudienceDto();
        when(audienceService.createAudience(any(AudienceDto.class))).thenReturn(createdAudience);

        // When & Then
        mockMvc.perform(post("/v1/audiences")
                .header("user-id", USER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(inputAudience)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Create success"))
                .andExpect(jsonPath("$.data.id").value(AUDIENCE_ID));

        verify(audienceService).createAudience(any(AudienceDto.class));
    }

    @Test
    @DisplayName("POST /v1/audiences - Should return 400 when user-id header is missing")
    void createAudience_WithoutUserIdHeader_ShouldReturnBadRequest() throws Exception {
        // Given
        AudienceDto inputAudience = createSampleAudienceDto();

        // When & Then
        mockMvc.perform(post("/v1/audiences")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(inputAudience)))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("POST /v1/audiences - Should return 400 when request body is empty")
    void createAudience_WithEmptyBody_ShouldReturnBadRequest() throws Exception {
        // When & Then
        mockMvc.perform(post("/v1/audiences")
                .header("user-id", USER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(""))
                .andExpect(status().isBadRequest());
    }

    // ========== PUT /v1/audiences/{id} Tests ==========

    @Test
    @DisplayName("PUT /v1/audiences/{id} - Should update audience successfully")
    void updateAudience_WithValidData_ShouldReturnSuccess() throws Exception {
        // Given
        AudienceDto updateAudience = AudienceDto.builder()
                .name("Updated Audience")
                .description("Updated Description")
                .build();

        AudienceDto updatedAudience = createSampleAudienceDto();
        updatedAudience.setName("Updated Audience");
        when(audienceService.updateAudience(any(AudienceDto.class))).thenReturn(updatedAudience);

        // When & Then
        mockMvc.perform(put("/v1/audiences/{id}", AUDIENCE_ID)
                .header("user-id", USER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateAudience)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Update success"));

        verify(audienceService).updateAudience(any(AudienceDto.class));
    }

    @Test
    @DisplayName("PUT /v1/audiences/{id} - Should return 400 when user-id header is missing")
    void updateAudience_WithoutUserIdHeader_ShouldReturnBadRequest() throws Exception {
        // Given
        AudienceDto updateAudience = createSampleAudienceDto();

        // When & Then
        mockMvc.perform(put("/v1/audiences/{id}", AUDIENCE_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateAudience)))
                .andExpect(status().isBadRequest());
    }

    // ========== DELETE /v1/audiences/{id} Tests ==========

    @Test
    @DisplayName("DELETE /v1/audiences/{id} - Should delete audience successfully")
    void deleteAudience_WithValidId_ShouldReturnSuccess() throws Exception {
        // Given
        doNothing().when(audienceService).deleteAudience(HASHED_USER_ID, 1L);

        // When & Then
        mockMvc.perform(delete("/v1/audiences/{id}", AUDIENCE_ID)
                .header("user-id", USER_ID))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Delete success"))
                .andExpect(jsonPath("$.data.id").value(AUDIENCE_ID));

        verify(audienceService).deleteAudience(HASHED_USER_ID, 1L);
    }

    @Test
    @DisplayName("DELETE /v1/audiences/{id} - Should return 400 when user-id header is missing")
    void deleteAudience_WithoutUserIdHeader_ShouldReturnBadRequest() throws Exception {
        // When & Then
        mockMvc.perform(delete("/v1/audiences/{id}", AUDIENCE_ID))
                .andExpect(status().isBadRequest());
    }

    // ========== POST /v1/audiences/estimate Tests ==========

    @Test
    @DisplayName("POST /v1/audiences/estimate - Should return estimate successfully")
    void estimate_WithValidData_ShouldReturnSuccess() throws Exception {
        // Given
        AudienceDto inputAudience = createSampleAudienceDto();
        AudienceResult estimateResult = new AudienceResult();
        estimateResult.setSatisfiedProfile(500L);
        when(audienceService.estimate(any(AudienceDto.class))).thenReturn(estimateResult);

        // When & Then
        mockMvc.perform(post("/v1/audiences/estimate")
                .header("user-id", USER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(inputAudience)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Estimate success"));

        verify(audienceService).estimate(any(AudienceDto.class));
    }

    @Test
    @DisplayName("POST /v1/audiences/estimate - Should return 400 when user-id header is missing")
    void estimate_WithoutUserIdHeader_ShouldReturnBadRequest() throws Exception {
        // Given
        AudienceDto inputAudience = createSampleAudienceDto();

        // When & Then
        mockMvc.perform(post("/v1/audiences/estimate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(inputAudience)))
                .andExpect(status().isBadRequest());
    }

    // ========== POST /v1/audiences/preview-profiles Tests ==========

    @Test
    @DisplayName("POST /v1/audiences/preview-profiles - Should return preview successfully")
    void previewProfiles_WithValidData_ShouldReturnSuccess() throws Exception {
        // Given
        AudienceDto inputAudience = createSampleAudienceDto();
        ProfilesResponseDto previewResult = ProfilesResponseDto.builder()
                .profiles(Arrays.asList())
                .schema(Arrays.asList("id", "name", "email"))
                .build();
        when(audienceService.previewProfiles(any(AudienceDto.class))).thenReturn(previewResult);

        // When & Then
        mockMvc.perform(post("/v1/audiences/preview-profiles")
                .header("user-id", USER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(inputAudience)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Preview success"));

        verify(audienceService).previewProfiles(any(AudienceDto.class));
    }

    @Test
    @DisplayName("POST /v1/audiences/preview-profiles - Should return 400 when user-id header is missing")
    void previewProfiles_WithoutUserIdHeader_ShouldReturnBadRequest() throws Exception {
        // Given
        AudienceDto inputAudience = createSampleAudienceDto();

        // When & Then
        mockMvc.perform(post("/v1/audiences/preview-profiles")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(inputAudience)))
                .andExpect(status().isBadRequest());
    }

    // ========== GET /v1/audiences/{id}/profiles Tests ==========

    @Test
    @DisplayName("GET /v1/audiences/{id}/profiles - Should return profiles with default parameters")
    void detailProfiles_WithDefaultParameters_ShouldReturnSuccess() throws Exception {
        // Given
        ProfilesResponseDto profilesResponse = ProfilesResponseDto.builder()
                .profiles(Arrays.asList())
                .schema(Arrays.asList("id", "name", "email"))
                .build();
        AudienceResponse<ProfilesResponseDto> expectedResponse = new AudienceResponse<>(profilesResponse);
        when(audienceService.detailProfiles(eq(0), eq(10), eq(false), eq(HASHED_USER_ID), eq(1L)))
                .thenReturn(expectedResponse);

        // When & Then
        mockMvc.perform(get("/v1/audiences/{id}/profiles", AUDIENCE_ID)
                .header("user-id", USER_ID))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").exists());

        verify(audienceService).detailProfiles(0, 10, false, HASHED_USER_ID, 1L);
    }

    @Test
    @DisplayName("GET /v1/audiences/{id}/profiles - Should return profiles with custom parameters")
    void detailProfiles_WithCustomParameters_ShouldReturnSuccess() throws Exception {
        // Given
        ProfilesResponseDto profilesResponse = ProfilesResponseDto.builder()
                .profiles(Arrays.asList())
                .schema(Arrays.asList("id", "name", "email"))
                .build();
        AudienceResponse<ProfilesResponseDto> expectedResponse = new AudienceResponse<>(profilesResponse);
        when(audienceService.detailProfiles(eq(2), eq(50), eq(true), eq(HASHED_USER_ID), eq(1L)))
                .thenReturn(expectedResponse);

        // When & Then
        mockMvc.perform(get("/v1/audiences/{id}/profiles", AUDIENCE_ID)
                .header("user-id", USER_ID)
                .param("page", "2")
                .param("limit", "50")
                .param("export", "true"))
                .andExpect(status().isOk());

        verify(audienceService).detailProfiles(2, 50, true, HASHED_USER_ID, 1L);
    }

    @Test
    @DisplayName("GET /v1/audiences/{id}/profiles - Should limit to 100 when limit exceeds maximum")
    void detailProfiles_WithLimitExceedsMaximum_ShouldLimitTo100() throws Exception {
        // Given
        ProfilesResponseDto profilesResponse = ProfilesResponseDto.builder().build();
        AudienceResponse<ProfilesResponseDto> expectedResponse = new AudienceResponse<>(profilesResponse);
        when(audienceService.detailProfiles(eq(0), eq(100), eq(false), eq(HASHED_USER_ID), eq(1L)))
                .thenReturn(expectedResponse);

        // When & Then
        mockMvc.perform(get("/v1/audiences/{id}/profiles", AUDIENCE_ID)
                .header("user-id", USER_ID)
                .param("limit", "200"))
                .andExpect(status().isOk());

        verify(audienceService).detailProfiles(0, 100, false, HASHED_USER_ID, 1L);
    }

    // ========== GET /v1/audiences/check-name Tests ==========

    @Test
    @DisplayName("GET /v1/audiences/check-name - Should check name exists successfully")
    void checkName_WithValidName_ShouldReturnSuccess() throws Exception {
        // Given
        String audienceName = "Test Audience Name";
        when(audienceService.checkNameExists(HASHED_USER_ID, audienceName)).thenReturn(true);

        // When & Then
        mockMvc.perform(get("/v1/audiences/check-name")
                .header("user-id", USER_ID)
                .param("name", audienceName))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Check segmentation name success"))
                .andExpect(jsonPath("$.data").value(true));

        verify(audienceService).checkNameExists(HASHED_USER_ID, audienceName);
    }

    @Test
    @DisplayName("GET /v1/audiences/check-name - Should return 400 when name parameter is missing")
    void checkName_WithoutNameParameter_ShouldReturnBadRequest() throws Exception {
        // When & Then
        mockMvc.perform(get("/v1/audiences/check-name")
                .header("user-id", USER_ID))
                .andExpect(status().isBadRequest());
    }

    // ========== POST /v1/audiences/total-profiles Tests ==========

    @Test
    @DisplayName("POST /v1/audiences/total-profiles - Should return total profiles successfully")
    void getTotalProfiles_WithValidIds_ShouldReturnSuccess() throws Exception {
        // Given
        List<Long> audienceIds = Arrays.asList(1L, 2L, 3L);
        Map<Long, Long> totalProfilesMap = new HashMap<>();
        totalProfilesMap.put(1L, 100L);
        totalProfilesMap.put(2L, 200L);
        totalProfilesMap.put(3L, 150L);
        when(audienceService.getTotalProfilesOfAudience(HASHED_USER_ID, audienceIds))
                .thenReturn(totalProfilesMap);

        // When & Then
        mockMvc.perform(post("/v1/audiences/total-profiles")
                .header("user-id", USER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(audienceIds)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Get success"));

        verify(audienceService).getTotalProfilesOfAudience(HASHED_USER_ID, audienceIds);
    }

    @Test
    @DisplayName("POST /v1/audiences/total-profiles - Should return 400 when user-id header is missing")
    void getTotalProfiles_WithoutUserIdHeader_ShouldReturnBadRequest() throws Exception {
        // Given
        List<Long> audienceIds = Arrays.asList(1L, 2L, 3L);

        // When & Then
        mockMvc.perform(post("/v1/audiences/total-profiles")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(audienceIds)))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("POST /v1/audiences/total-profiles - Should handle empty audience IDs list")
    void getTotalProfiles_WithEmptyList_ShouldReturnSuccess() throws Exception {
        // Given
        List<Long> audienceIds = Arrays.asList();
        Map<Long, Long> emptyMap = new HashMap<>();
        when(audienceService.getTotalProfilesOfAudience(HASHED_USER_ID, audienceIds))
                .thenReturn(emptyMap);

        // When & Then
        mockMvc.perform(post("/v1/audiences/total-profiles")
                .header("user-id", USER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(audienceIds)))
                .andExpect(status().isOk());

        verify(audienceService).getTotalProfilesOfAudience(HASHED_USER_ID, audienceIds);
    }
}
