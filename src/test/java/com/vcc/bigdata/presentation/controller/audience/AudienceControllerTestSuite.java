package com.vcc.bigdata.presentation.controller.audience;

import org.junit.platform.suite.api.SelectClasses;
import org.junit.platform.suite.api.Suite;

/**
 * Test Suite for AudienceController
 * 
 * This suite runs all test classes related to AudienceController:
 * - Unit tests with mocked dependencies
 * - Integration tests with Spring context
 * 
 * To run this suite:
 * mvn test -Dtest=AudienceControllerTestSuite
 */
@Suite
@SelectClasses({
    AudienceControllerTest.class,
    AudienceControllerIntegrationTest.class
})
public class AudienceControllerTestSuite {
    // This class remains empty, it is used only as a holder for the above annotations
}
