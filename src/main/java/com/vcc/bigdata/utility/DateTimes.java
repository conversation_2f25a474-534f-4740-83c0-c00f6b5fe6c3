package com.vcc.bigdata.utility;

import com.vcc.bigdata.domain.model.DateTimeFilter;
import com.vcc.bigdata.domain.model.DateTimeRangeFilter;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.sql.Timestamp;
import java.util.Date;
import java.util.Locale;

/**
 * <AUTHOR>
 *         Working with time
 */
public class DateTimes {
    public static final String DATE_FM = "yyyy-MM-dd";
    public static final String YEAR = "yyyy";
    public static final String MONTH = "yyyy-MM-01";
    public static final String DATE_TIME_FM = "yyyy-MM-dd HH:mm:ss";
    private static final String DATE_FM_ES = "yyyy.MM";

    public static Date from(Long timestamp, String format) {
        try {
            DateTimeFormatter formatter = DateTimeFormat.forPattern(format);
            DateTime date = formatter.parseDateTime(formatter.print(timestamp));
            return date.toCalendar(Locale.US).getTime();
        } catch (Exception ignore) {
            return null;
        }
    }

    public static Date from(String timeString, String format) {
        try {
            DateTimeFormatter formatter = DateTimeFormat.forPattern(format);
            DateTime date = formatter.parseDateTime(timeString);
            return date.toCalendar(Locale.US).getTime();
        } catch (Exception ignore) {
            return null;
        }
    }

    public static Date toDate(Long timestamp) {
        return from(timestamp, DATE_FM);
    }

    public static Date toDate(String timeString) {
        return from(timeString, DATE_FM);
    }

    public static Date toDateTime(String timeString) {
        return from(timeString, DATE_TIME_FM);
    }

    public static Date toDateTime(Long timestamp) {
        return from(timestamp, DATE_TIME_FM);
    }

    public static String toString(Long timestamp, String format) {
        try {
            DateTimeFormatter formatter = DateTimeFormat.forPattern(format);
            return formatter.print(timestamp);
        } catch (Exception ignore) {
            return null;
        }
    }

    public static String getTodayDateEs() {
        return toString(System.currentTimeMillis(), DATE_FM_ES);
    }

    public static String getDateFmEs(Timestamp timestamp) {
        return toString(timestamp.getTime(), DATE_FM_ES);
    }

    public static String toDateString(Long timestamp) {
        return toString(timestamp, DATE_FM);
    }

    public static String toDateTimeString(Long timestamp) {
        return toString(timestamp, DATE_TIME_FM);
    }

    /**
     * Parse a single date time string into a DateTimeFilter object
     * 
     * @param dateTime format: yyyy|MM|dd|HH|mm|ss
     * @return DateTimeFilter object
     */
    public static DateTimeFilter parseSingleDateTime(String dateTime) {
        String[] tokens = dateTime.split("\\|");
        DateTimeFilter dateTimeFilter = new DateTimeFilter();
        dateTimeFilter.setYear(tokens[0].equals("*") ? null : Integer.parseInt(tokens[0]));
        dateTimeFilter.setMonth(tokens[1].equals("*") ? null : Integer.parseInt(tokens[1]));
        dateTimeFilter.setDay(tokens[2].equals("*") ? null : Integer.parseInt(tokens[2]));
        dateTimeFilter.setHour(tokens[3].equals("*") ? null : Integer.parseInt(tokens[3]));
        dateTimeFilter.setMinute(tokens[4].equals("*") ? null : Integer.parseInt(tokens[4]));
        dateTimeFilter.setSecond(tokens[5].equals("*") ? null : Integer.parseInt(tokens[5]));
        return dateTimeFilter;
    }

    /**
     * Parse a range of date time strings into a DateTimeRangeFilter object
     * 
     * @param dateTime format: yyyy|MM|dd|HH|mm|ss&yyyy|MM|dd|HH|mm|ss
     * @return DateTimeRangeFilter object
     */
    public static DateTimeRangeFilter parseDateTimeRange(String dateTime) {
        String[] tokens = dateTime.split("&");
        DateTimeRangeFilter dateTimeRangeFilter = new DateTimeRangeFilter();
        dateTimeRangeFilter.setStart(parseSingleDateTime(tokens[0]));
        dateTimeRangeFilter.setEnd(parseSingleDateTime(tokens[1]));
        return dateTimeRangeFilter;
    }
}
