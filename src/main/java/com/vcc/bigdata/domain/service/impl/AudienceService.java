package com.vcc.bigdata.domain.service.impl;

import com.vcc.bigdata.application.dto.AudienceDto;
import com.vcc.bigdata.application.dto.ProfileManagementDto;
import com.vcc.bigdata.application.dto.ProfilesResponseDto;
import com.vcc.bigdata.domain.entity.Audience;
import com.vcc.bigdata.domain.entity.ProfileManagement;
import com.vcc.bigdata.domain.exception.InvalidParamException;
import com.vcc.bigdata.domain.exception.audience.AudienceNotFoundException;
import com.vcc.bigdata.domain.exception.profile.ProfileManagementNotFound;
import com.vcc.bigdata.domain.model.AudienceResult;
import com.vcc.bigdata.domain.model.MaskingDetail;
import com.vcc.bigdata.domain.model.Person;
import com.vcc.bigdata.domain.repository.IAudienceDao;
import com.vcc.bigdata.domain.repository.IPersonDao;
import com.vcc.bigdata.domain.repository.IProfileManagementDao;
import com.vcc.bigdata.domain.service.IAudienceService;
import com.vcc.bigdata.domain.service.IKmsService;
import com.vcc.bigdata.presentation.mapper.AudienceMapper;
import com.vcc.bigdata.presentation.mapper.ProfileManagementMapper;
import com.vcc.bigdata.presentation.mapper.QueryBuilderES;
import com.vcc.bigdata.presentation.response.audience.AudienceResponse;
import com.vcc.bigdata.shared.Constants;
import com.vcc.bigdata.utility.DateTimes;
import com.vcc.bigdata.utility.SequenceGenerator;
import com.vcc.bigdata.utility.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AudienceService implements IAudienceService {
    private final IAudienceDao audienceDao;
    private final IProfileManagementDao profileManagementDao;
    private final SequenceGenerator sequenceGenerator;
    private final IPersonDao personDao;
    private final IKmsService kmsService;

    public AudienceService(IAudienceDao audienceDao, IProfileManagementDao profileManagementDao,
                           IPersonDao personDao, IKmsService kmsService) {
        this.audienceDao = audienceDao;
        this.profileManagementDao = profileManagementDao;
        this.personDao = personDao;
        this.kmsService = kmsService;
        this.sequenceGenerator = SequenceGenerator.getInstance();
    }

    @Override
    public AudienceResponse<List<AudienceDto>> getAll(Long userId, Integer shared, String sort, Integer page, Integer limit, String search,
                                              String fromDate, String toDate, String dateField, Integer type) {
        // initialize pageable with page and limit
        Pageable pageable = PageRequest.of(page, limit);

        // process sort string with format "field_1:order,field_2:order"
        if (Strings.isNotNullOrEmpty(sort)) {
            List<Sort.Order> orders = new ArrayList<>();
            String[] fields = sort.split(",");
            for (String field : fields) {
                String[] fieldOrder = field.split(":");
                if (fieldOrder.length == 2) {
                    String fieldName = fieldOrder[0];
                    String order = fieldOrder[1];
                    if (order.equals("asc")) {
                        orders.add(Sort.Order.asc(fieldName));
                    } else if (order.equals("desc")) {
                        orders.add(Sort.Order.desc(fieldName));
                    }
                }
            }
            // if orders is not empty, then set pageable with sort
            if (!orders.isEmpty()) pageable = PageRequest.of(page, limit, Sort.by(orders));
        }

        // process date range
        Timestamp fromTimestamp = null;
        Timestamp toTimestamp = null;

        if (Strings.isNotNullOrEmptyAll(fromDate, toDate)) {
            if (fromDate != null && !fromDate.isEmpty()) {
                Date fromDateObj = DateTimes.toDate(fromDate);
                fromTimestamp = new Timestamp(fromDateObj.getTime());
            }

            if (toDate != null && !toDate.isEmpty()) {
                Date toDateObj = DateTimes.toDateTime(toDate + " 23:59:59");
                toTimestamp = new Timestamp(toDateObj.getTime());
            }
        }

        AudienceResponse<List<AudienceDto>> resultResponse = new AudienceResponse<>();
        resultResponse.getMeta().setPage(page);
        resultResponse.getMeta().setLimit(limit);

        // get audiences
        Page<Audience> audiences;
        // Handle search parameter
        if (Strings.isNotNullOrEmpty(search)) {
            // Search by name or description containing the search term
            if (Strings.isNotNullOrEmpty(dateField)) {
                if (dateField.equals("createdAt") && fromTimestamp != null && toTimestamp != null) {
                    audiences = audienceDao.searchAudiencesWithCreatedDateRange(
                            userId, Constants.BOOLEAN_FALSE, search, fromTimestamp, toTimestamp, pageable);
                } else if (dateField.equals("updatedAt") && fromTimestamp != null && toTimestamp != null) {
                    audiences = audienceDao.searchAudiencesWithUpdatedDateRange(
                            userId, Constants.BOOLEAN_FALSE, search, fromTimestamp, toTimestamp, pageable);
                } else throw new InvalidParamException("date_field need to go with from_date and to_date", "Invalid date field");

            } else {
                audiences = audienceDao.searchAudiences(userId, Constants.BOOLEAN_FALSE, search, pageable);
            }
        } else {
            if (Strings.isNotNullOrEmpty(dateField)) {
                if (dateField.equals("createdAt") && fromTimestamp != null && toTimestamp != null) {
                    audiences = audienceDao.getAudiencesByUserIdAndIsDeletedAndCreatedAtBetween(
                            userId, Constants.BOOLEAN_FALSE, fromTimestamp, toTimestamp, pageable);
                } else if (dateField.equals("updatedAt") && fromTimestamp != null && toTimestamp != null) {
                    audiences = audienceDao.getAudiencesByUserIdAndIsDeletedAndUpdatedAtBetween(
                            userId, Constants.BOOLEAN_FALSE, fromTimestamp, toTimestamp, pageable);
                } else throw new InvalidParamException("date_field need to go with from_date and to_date", "Invalid date field");
            } else {
                // Default query without search or date filters
                pageable = PageRequest.of(page, limit, Sort.by(Sort.Order.desc("id")));
                audiences = audienceDao.getAudiencesByUserIdAndIsDeleted(userId, Constants.BOOLEAN_FALSE, pageable);
            }
        }
        resultResponse.getMeta().setTotal(audienceDao.countAudiencesByUserIdAndIsDeleted(userId, Constants.BOOLEAN_FALSE));
        resultResponse.setData(audiences.getContent().stream().map(AudienceMapper::entity2Dto).collect(Collectors.toList()));
        return resultResponse;
    }

    @Override
    public AudienceDto getAudience(Long userId, Long id) {
        Audience audience = audienceDao.getAudienceByIdAndIsDeleted(id, 0);
        if (audience == null) throw new AudienceNotFoundException("Id not found", "Invalid audience id");
        return AudienceMapper.entity2Dto(audience);
    }

    @Override
    public AudienceDto createAudience(AudienceDto audienceDto) {
        // validate
        if (audienceDto.getName() == null) {
            throw new InvalidParamException("Name is required", "Missing audience name");
        }
        audienceDto.setId(String.valueOf(sequenceGenerator.nextId()));
        audienceDto.setIsDeleted(Constants.BOOLEAN_FALSE);
        audienceDto.setType(Constants.AUDIENCE_TYPE_STATIC);
        Audience audience = AudienceMapper.dto2Entity(audienceDto);
        audienceDao.save(audience);
        return audienceDto;
    }

    @Override
    public AudienceDto updateAudience(AudienceDto audienceDto) {
        Audience audience = audienceDao.getAudienceByIdAndIsDeleted(Long.valueOf(audienceDto.getId()), 0);
        if (audience == null) throw new AudienceNotFoundException("Id not found", "Invalid audience id");
        AudienceDto currentAudienceDto = AudienceMapper.entity2Dto(audience);
        currentAudienceDto.update(audienceDto);
        audienceDao.save(audience);
        return audienceDto;
    }

    @Override
    public void deleteAudience(Long userId, Long id) {
        Audience audience = audienceDao.getAudienceByIdAndIsDeleted(id, 0);
        if (audience == null) throw new AudienceNotFoundException("Id not found", "Invalid audience id");
        audience.setIsDeleted(1);
        audienceDao.save(audience);
    }

    @Override
    public AudienceResult estimate(AudienceDto audienceDto) {
        Long userOwner = Long.valueOf(audienceDto.getUserId());
        if (audienceDto.getId() != null) {
            Audience audience = audienceDao.getAudienceByIdAndIsDeleted(Long.valueOf(audienceDto.getId()), 0);
            userOwner = audience.getUserId();
        }
        ProfileManagementDto profileManagementDto = getProfileManagement(userOwner);

        AudienceResult audienceResult = new AudienceResult();
        audienceResult.setTotalProfile(personDao.getTotal(String.valueOf(userOwner)));

        if (audienceDto.getSegmentRule() == null)
            audienceResult.setTotalProfile(audienceResult.getTotalProfile());
        else {
            Map<String, ProfileManagementDto.Mapping> mappingMap = new HashMap<>();
            profileManagementDto.getMapping().forEach(mapping -> mappingMap.put(mapping.getFieldName(), mapping));

            Map<String, ProfileManagementDto.Mapping> mappingMapIncludeSubField = mappingMap.entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            mappingMapIncludeSubField.putAll(getMapSubfieldMapping(profileManagementDto.getMapping()));

            try {
                audienceResult.setSatisfiedProfile(personDao.estimateSegment(String.valueOf(userOwner),
                        QueryBuilderES.getSegmentQuery(audienceDto.getSegmentRule(), mappingMapIncludeSubField)));
            } catch (Exception e) {
                throw new RuntimeException("Error when estimate audience: " + e.getMessage());
            }
        }

        return audienceResult;
    }

    ProfileManagementDto getProfileManagement(Long userId) {
        ProfileManagement profileManagement = profileManagementDao.getProfileManagementByUserId(userId);
        if (profileManagement == null) throw new ProfileManagementNotFound("Profile management not found", "Invalid user id");
        return ProfileManagementMapper.entity2Dto(profileManagement);
    }

    private Map<String, ProfileManagementDto.Mapping> getMapSubfieldMapping(List<ProfileManagementDto.Mapping> parentMappings) {
        return getAllSubfieldMapping(parentMappings).stream().collect(Collectors.toMap(ProfileManagementDto.Mapping::getFieldName, Function.identity()));
    }

    private List<ProfileManagementDto.Mapping> getAllSubfieldMapping(List<ProfileManagementDto.Mapping> parentMappings) {
        List<ProfileManagementDto.Mapping> allSubFieldMappings = new ArrayList<>();
        parentMappings.stream().filter(mapping -> mapping.getSubFields() != null && !mapping.getSubFields().isEmpty()).forEach(mapping -> {
            String parentField = mapping.getFieldName();
            mapping.getSubFields().values().forEach(subFieldMapping -> {
                ProfileManagementDto.Mapping mappingCopy = new ProfileManagementDto.Mapping();
                mappingCopy.setFieldName(parentField + "." + subFieldMapping.getFieldName());
                mappingCopy.setFieldMapping(subFieldMapping.getFieldMapping());
                mappingCopy.setDataType(subFieldMapping.getDataType());
                mappingCopy.setDemographic(subFieldMapping.getDemographic());
                mappingCopy.setMaskingType(subFieldMapping.getMaskingType());
                mappingCopy.setSubFields(subFieldMapping.getSubFields());

                allSubFieldMappings.add(mappingCopy);
                allSubFieldMappings.addAll(getAllSubfieldMapping(Collections.singletonList(mappingCopy)));
            });
        });
        return allSubFieldMappings;
    }

    @Override
    public ProfilesResponseDto previewProfiles(AudienceDto audienceDto) {
        List<MaskingDetail> maskingDetails = null;
        if (audienceDto.getId() != null) {
//            Audience audience = audienceDao.getAudienceByIdAndIsDeleted(Long.valueOf(audienceDto.getId()), 0);
//            maskingDetails = permissionDao.getMaskingSegmentPermission(userOwner, segmentation.getId());
//            userOwner = segmentation.getUserId();
        }
        ProfileManagementDto profileManagementDto = getProfileManagement(Long.valueOf(audienceDto.getUserId()));

        ProfilesResponseDto resultResponse = new ProfilesResponseDto();
        resultResponse.setSchema(new ArrayList<>());

        Map<String, ProfileManagementDto.Mapping> mappingMap = new HashMap<>();
        profileManagementDto.getMapping().forEach(mapping -> {
            mappingMap.put(mapping.getFieldName(), mapping);
            resultResponse.getSchema().add(mapping.getFieldMapping());
        });

        AtomicBoolean hasSystemData = new AtomicBoolean(false);
        if (maskingDetails != null)
            maskingDetails.forEach(maskingDetail -> {
                if (!mappingMap.get(maskingDetail.getFieldName()).getMaskingType().equals(Constants.MASKING_SYSTEM))
                    mappingMap.get(maskingDetail.getFieldName()).setMaskingType(maskingDetail.getMaskingType());
                else hasSystemData.set(true);
            });

        Map<String, ProfileManagementDto.Mapping> mappingMapIncludeSubfield = mappingMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        mappingMapIncludeSubfield.putAll(getMapSubfieldMapping(profileManagementDto.getMapping()));

        BoolQueryBuilder boolQueryBuilder = null;
        try {
            boolQueryBuilder = QueryBuilderES.getSegmentQuery(audienceDto.getSegmentRule(), mappingMapIncludeSubfield);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        if (boolQueryBuilder != null) {
            hasSystemData.set(false);
            audienceDto.getSegmentRule().getGroups().forEach(group -> group.getRules().forEach(rule -> {
                if (mappingMapIncludeSubfield.get(rule.getFieldName()).getMaskingType().equals(Constants.MASKING_SYSTEM))
                    hasSystemData.set(true);
            }));
        }

        List<Person> people = getFinalPeople(String.valueOf(audienceDto.getUserId()), boolQueryBuilder, mappingMap, profileManagementDto.getKmsCustomerId(), hasSystemData.get(), 0, 20);
        resultResponse.setProfiles(people);
        return resultResponse;
    }

    private List<Person> getFinalPeople(String userId,
                                        BoolQueryBuilder boolQueryBuilder,
                                        Map<String, ProfileManagementDto.Mapping> mappingMap,
                                        String kmsCustomerKey,
                                        boolean hasSystemData,
                                        Integer page,
                                        Integer limit) {
        List<Person> people = personDao.getBySegment(userId, boolQueryBuilder, page, limit);
        people.parallelStream().forEach(person -> {
            // decrypt person
            Map<String, List<Person.Element>> data = person.getData();
            person.setData(new HashMap<>());
            data.forEach((key, value) -> {
                ProfileManagementDto.Mapping mapping = mappingMap.get(key);
                if (Objects.isNull(mapping)) return;
                if (hasSystemData) mapping.setMaskingType(Constants.MASKING_SYSTEM);

                value.forEach(element -> {
                    if (mapping.getMaskingType().equals(Constants.MASKING_SYSTEM))
                        element.setValue(StringUtils.repeat("*", 8));
                    else if (mapping.getMaskingType().equals(Constants.MASKING_FULL))
                        element.setValue(StringUtils.repeat("*", 8));
                    else {
                        String elementValue = kmsService.decrypt(kmsCustomerKey, element.getValue().toString());

                        if (!Strings.isNullOrEmpty(elementValue)) {
                            if (mapping.getDataType().equals(Constants.DATA_TYPE_DATE)) {
                                if (elementValue.matches("\\d+")) {
                                    elementValue = DateTimes.toDateString(Long.parseLong(elementValue));
                                }
                            } else if (mapping.getDataType().equals(Constants.DATA_TYPE_DATETIME)) {
                                if (elementValue.matches("\\d+")) {
                                    elementValue = DateTimes.toDateTimeString(Long.parseLong(elementValue));
                                }
                            }

                            if (mapping.getMaskingType().equals(Constants.MASKING_PART)) {
                                if (mapping.getDemographic().equals(Constants.DEMOGRAPHIC_EMAIL))
                                    elementValue = StringUtils.repeat("*", elementValue.split("@")[0].length()) + "@" + elementValue.split("@")[1];
                                else if (mapping.getDemographic().equals(Constants.DEMOGRAPHIC_PHONE))
                                    elementValue = StringUtils.repeat("*", 6) + elementValue.substring(6, 10);
                                else
                                    elementValue = elementValue.substring(0, elementValue.length() / 2) + StringUtils.repeat("*", 4);
                            }
                        }

                        element.setValue(elementValue);
                    }
                });

                person.getData().put(mappingMap.get(key).getFieldMapping(), value);
                if (mapping.getDemographic().equals(Constants.DEMOGRAPHIC_FULLNAME) && !mapping.getMaskingType().equals(Constants.MASKING_FULL))
                    person.setProfileName(value.get(0).getValue().toString());
            });
        });
        return people;
    }

    @Override
    public AudienceResponse<ProfilesResponseDto> detailProfiles(Integer page, Integer limit, boolean export, Long userId, Long id) {
        Audience audience = audienceDao.getAudienceByIdAndIsDeleted(id, 0);
        if (audience == null) throw new AudienceNotFoundException("Id not found", "Invalid audience id");
//        if (!audience.getUserId().equals(userId) && !permissionDao.checkSegmentPermission(userId, segmentationId, Constants.PERMISSION_SEGMENT_VIEW))
//            throw new Exception(ClientErrorHandle.USER_NOT_PERMISSION_CODE);

        AudienceDto audienceDto = AudienceMapper.entity2Dto(audience);

        ProfileManagementDto profileManagementDto = getProfileManagement(audience.getUserId());

        Map<String, ProfileManagementDto.Mapping> mappingMap = new HashMap<>();
        profileManagementDto.getMapping().forEach(mapping -> mappingMap.put(mapping.getFieldName(), mapping));

        AtomicBoolean hasSystemData = new AtomicBoolean(false);
//        if (!audience.getUserId().equals(userId)) {
//            List<MaskingDetail> maskingDetails = permissionDao.getMaskingSegmentPermission(userId, segmentationId);
//            maskingDetails.forEach(maskingDetail -> {
//                if (!mappingMap.get(maskingDetail.getFieldName()).getMaskingType().equals(Constants.MASKING_SYSTEM))
//                    mappingMap.get(maskingDetail.getFieldName()).setMaskingType(maskingDetail.getMaskingType());
//                else hasSystemData.set(true);
//            });
//        }

        Map<String, ProfileManagementDto.Mapping> mappingMapIncludeSubField = mappingMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        mappingMapIncludeSubField.putAll(getMapSubfieldMapping(profileManagementDto.getMapping()));

        AudienceResponse<ProfilesResponseDto> result = new AudienceResponse<>();
        ProfilesResponseDto profilesResponseDto = new ProfilesResponseDto();
        profilesResponseDto.setSchema(new ArrayList<>());
        profileManagementDto.getMapping().forEach(mapping -> profilesResponseDto.getSchema().add(mapping.getFieldMapping()));
        try {
            BoolQueryBuilder boolQueryBuilder = QueryBuilderES.getSegmentQuery(audienceDto.getSegmentRule(), mappingMapIncludeSubField);
                if (boolQueryBuilder != null) {
                    hasSystemData.set(false);
                    audienceDto.getSegmentRule().getGroups().forEach(group -> group.getRules().forEach(rule -> {
                        if (mappingMapIncludeSubField.get(rule.getFieldName()).getMaskingType().equals(Constants.MASKING_SYSTEM))
                            hasSystemData.set(true);
                    }));
                }

                List<Person> people = getFinalPeople(audienceDto.getUserId(), boolQueryBuilder, mappingMap, profileManagementDto.getKmsCustomerId(), hasSystemData.get(), page, limit);
                profilesResponseDto.setProfiles(people);
                result.getMeta().setTotal(Math.toIntExact(personDao.estimateSegment(audienceDto.getUserId(), boolQueryBuilder)));
                result.getMeta().setPage(page);
                result.getMeta().setLimit(limit);
                result.setData(profilesResponseDto);

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return result;
    }

    @Override
    public boolean checkNameExists(Long userId, String name) {
        Audience audience = audienceDao.getAudienceByNameAndUserIdAndIsDeleted(name, userId, Constants.BOOLEAN_FALSE);
        return audience != null;
    }

    @Override
    public Map<Long, Long> getTotalProfilesOfAudience(Long userId, List<Long> audienceIds) {
        // Map<audienceId, total_profiles>
        Map<Long, Long> segmentIdAndTotalProfile = new ConcurrentHashMap<>();
        // Lấy danh sách audienceDto dựa vào audienceIds
        List<AudienceDto> audienceDtos = audienceDao.getAllByIdIn(audienceIds).stream()
                .map(AudienceMapper::entity2Dto)
                .collect(Collectors.toList());

        // Tạo field_name mapping
        ProfileManagementDto profileManagementDto = getProfileManagement(userId);
        if (Objects.isNull(profileManagementDto.getMapping())) {
            return segmentIdAndTotalProfile;
        }
        Map<String, ProfileManagementDto.Mapping> mappingMap = new ConcurrentHashMap<>();
        profileManagementDto.getMapping().forEach(mapping -> mappingMap.put(mapping.getFieldName(), mapping));
        mappingMap.putAll(getMapSubfieldMapping(profileManagementDto.getMapping()));

        // Lặp qua từng audienceDto
        audienceDtos.parallelStream().forEach(audienceDto -> {
            Long audienceId = Long.valueOf(audienceDto.getId());
            // Xử lý segmentRule
            if (audienceDto.getSegmentRule() != null) {
                for (AudienceDto.Group group : audienceDto.getSegmentRule().getGroups()) {
                    for (AudienceDto.Rule rule : group.getRules()) {
                        ProfileManagementDto.Mapping mapping = mappingMap.get(rule.getFieldName());
                        if (mapping != null) {
                            rule.setFieldMapping(mapping.getFieldMapping());
                            rule.setDataType(mapping.getDataType());
                            rule.setSystemData(Constants.MASKING_SYSTEM.equals(mapping.getMaskingType()));
                        }
                    }
                }
            }

            // Estimate totalProfiles từ Elasticsearch
            Long totalProfiles = estimateProfiles(profileManagementDto, audienceDto, mappingMap);
            // Kiểm tra kết quả query từ Elasticsearch
            if (Objects.nonNull(totalProfiles)) {
                // Thành công: Update totalProfiles cho từng segmentation trong bảng segmentation.
                updateAudienceTotalProfiles(audienceId, totalProfiles);
            } else {
                // Có lỗi hoặc thời gian thực hiện query vượt quá 2 giây: Lấy totalProfiles từ bảng audiences
                totalProfiles = audienceDao.getAudienceByIdAndIsDeleted(audienceId, Constants.BOOLEAN_FALSE).getProfileCount();
            }
            // Put segmentationId,totalProfiles vào Map<audienceId, total_profiles>
            segmentIdAndTotalProfile.put(audienceId, Objects.nonNull(totalProfiles) ? totalProfiles : 0L);
        });

        return segmentIdAndTotalProfile;
    }

    // Estimate profiles với timeout ngắn để tránh treo hệ thống
    private Long estimateProfiles(ProfileManagementDto profileManagementDto, AudienceDto audienceDto,
                                  Map<String, ProfileManagementDto.Mapping> mappingMap) {
        try {
            return CompletableFuture.supplyAsync(() -> {
                        try {
                            return personDao.estimateSegment(profileManagementDto.getUserId(),
                                    QueryBuilderES.getSegmentQuery(audienceDto.getSegmentRule(), mappingMap));
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
            ).get(2, TimeUnit.SECONDS); // Chờ tối đa 2 giây cho mỗi query
        } catch (TimeoutException e) {
            log.error("Estimate segmentation timed out: {}", e.getMessage());
            return null;
        } catch (Exception e) {
            log.error("Estimate segmentation error: {}", e.getMessage());
            return null;
        }
    }

    // Update profile_count cho từng audience trong bảng audiences.
    private void updateAudienceTotalProfiles(Long audienceId, Long totalProfiles) {
        Audience audience = audienceDao.getAudienceByIdAndIsDeleted(audienceId, Constants.BOOLEAN_FALSE);
        // Update profile_count nếu có sự thay đổi
        if (!totalProfiles.equals(audience.getProfileCount())) {
            audience.setProfileCount(totalProfiles);
            audienceDao.save(audience);
        }
    }
}
