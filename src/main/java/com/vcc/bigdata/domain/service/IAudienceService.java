package com.vcc.bigdata.domain.service;

import com.vcc.bigdata.application.dto.AudienceDto;
import com.vcc.bigdata.application.dto.ProfilesResponseDto;
import com.vcc.bigdata.domain.model.AudienceResult;
import com.vcc.bigdata.presentation.response.ResultResponse;
import com.vcc.bigdata.presentation.response.audience.AudienceResponse;

import java.util.List;
import java.util.Map;

public interface IAudienceService {
    AudienceResponse<List<AudienceDto>> getAll(Long userId, Integer shared, String sort, Integer page, Integer limit, String search,
                                       String fromDate, String toDate, String dateField, Integer type);

    AudienceDto getAudience(Long userId, Long id);

    AudienceDto createAudience(AudienceDto audienceDto);

    AudienceDto updateAudience(AudienceDto audienceDto);

    void deleteAudience(Long userId, Long id);

    AudienceResult estimate(AudienceDto audienceDto);

    ProfilesResponseDto previewProfiles(AudienceDto audienceDto);

    AudienceResponse<ProfilesResponseDto> detailProfiles(Integer page, Integer limit, boolean export, Long userId, Long id);

    boolean checkNameExists(Long userId, String name);

    Map<Long, Long> getTotalProfilesOfAudience(Long userId, List<Long> audienceIds);
}
