package com.vcc.bigdata.domain.repository;

import com.vcc.bigdata.domain.entity.Audience;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.sql.Timestamp;
import java.util.Collection;
import java.util.List;

@Repository
public interface IAudienceDao extends JpaRepository<Audience, Long> {
    Page<Audience> getAudiencesByUserIdAndIsDeletedAndCreatedAtBetween(Long userId, Integer isDeleted, Timestamp from, Timestamp to, Pageable pageable);
    Page<Audience> getAudiencesByUserIdAndIsDeletedAndUpdatedAtBetween(Long userId, Integer isDeleted, Timestamp from, Timestamp to, Pageable pageable);
    Audience getAudienceByNameAndUserIdAndIsDeleted(String name, Long userId, Integer isDeleted);
    Audience getAudienceByIdAndIsDeleted(Long id, Integer isDeleted);

    @Query(value = "SELECT * FROM audiences WHERE user_id = :userId AND is_deleted = :isDeleted " +
           "AND (LOWER(name) LIKE LOWER(CONCAT('%', :search, '%')) OR LOWER(description) LIKE LOWER(CONCAT('%', :search, '%'))) " +
           "ORDER BY id DESC",
           nativeQuery = true)
    Page<Audience> searchAudiences(
        @Param("userId") Long userId,
        @Param("isDeleted") Integer isDeleted,
        @Param("search") String search,
        Pageable pageable);

    @Query(value = "SELECT * FROM audiences WHERE user_id = :userId AND is_deleted = :isDeleted " +
           "AND (LOWER(name) LIKE LOWER(CONCAT('%', :search, '%')) OR LOWER(description) LIKE LOWER(CONCAT('%', :search, '%'))) " +
           "AND created_at BETWEEN :fromDate AND :toDate " +
           "ORDER BY id DESC",
           nativeQuery = true)
    Page<Audience> searchAudiencesWithCreatedDateRange(
        @Param("userId") Long userId,
        @Param("isDeleted") Integer isDeleted,
        @Param("search") String search,
        @Param("fromDate") Timestamp fromDate,
        @Param("toDate") Timestamp toDate,
        Pageable pageable);

    @Query(value = "SELECT * FROM audiences WHERE user_id = :userId AND is_deleted = :isDeleted " +
           "AND (LOWER(name) LIKE LOWER(CONCAT('%', :search, '%')) OR LOWER(description) LIKE LOWER(CONCAT('%', :search, '%'))) " +
           "AND updated_at BETWEEN :fromDate AND :toDate " +
           "ORDER BY id DESC",
           nativeQuery = true)
    Page<Audience> searchAudiencesWithUpdatedDateRange(
        @Param("userId") Long userId,
        @Param("isDeleted") Integer isDeleted,
        @Param("search") String search,
        @Param("fromDate") Timestamp fromDate,
        @Param("toDate") Timestamp toDate,
        Pageable pageable);

    Page<Audience> getAudiencesByUserIdAndIsDeleted(Long userId, Integer isDeleted, Pageable pageable);

    int countAudiencesByUserIdAndIsDeleted(Long userId, Integer isDeleted);

    List<Audience> getAllByIdIn(List<Long> audienceIds);
}
