package com.vcc.bigdata.application.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.sql.Timestamp;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
@Builder
public class AudienceDto {
    private String id;

    private String name;

    private String description;

    @JsonProperty("user_id")
    private String userId;

    @JsonProperty("is_deleted")
    private Integer isDeleted;

    @JsonProperty("created_at")
    private Timestamp createdAt;

    @JsonProperty("updated_at")
    private Timestamp updatedAt;

    @JsonProperty("segment_rule")
    private SegmentRule segmentRule;

    @JsonProperty("profile_count")
    private Long profileCount;

    private int type;

    // external data
    @JsonProperty("satisfied_profile_count")
    private Long satisfiedProfileCount;

    @JsonProperty("profile_management_id")
    private String profileManagementId;

    private List<Integer> permission;

    @JsonProperty("is_shared")
    private boolean isShared = false;

    private List<ProfileManagementDto.Mapping> mapping;

    public void update(AudienceDto audienceDto) {
        if (audienceDto.getName() != null) this.name = audienceDto.getName();
        if (audienceDto.getDescription() != null) this.description = audienceDto.getDescription();
        if (audienceDto.getSegmentRule() != null) this.segmentRule = audienceDto.getSegmentRule();
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class SegmentRule {
        @JsonProperty("group_expression")
        private String groupExpression;

        private List<Group> groups;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Group {
        @JsonProperty("rule_expression")
        private String ruleExpression;

        private List<Rule> rules;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Rule {
        @JsonProperty("field_name")
        private String fieldName;

        @JsonProperty("data_type")
        private String dataType;

        @JsonProperty("field_mapping")
        private String fieldMapping;

        @JsonProperty("is_system_data")
        private boolean isSystemData = false;

        private String compare;

        private Object value;
        // pattern only apply for field frequency_url (hashcode: 790a5b83)
        private String pattern;

        public Rule(String fieldName, String dataType, String compare, Object value) {
            this.fieldName = fieldName;
            this.dataType = dataType;
            this.compare = compare;
            this.value = value;
        }

    }
}
