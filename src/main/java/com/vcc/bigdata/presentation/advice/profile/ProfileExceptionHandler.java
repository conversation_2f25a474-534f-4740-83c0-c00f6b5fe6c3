package com.vcc.bigdata.presentation.advice.profile;

import com.vcc.bigdata.domain.exception.profile.ProfileManagementNotFound;
import com.vcc.bigdata.presentation.response.ErrorResponse;
import com.vcc.bigdata.presentation.response.Meta;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.Collections;

@RestControllerAdvice
@Order(Ordered.HIGHEST_PRECEDENCE)
public class ProfileExceptionHandler {
    @ExceptionHandler(ProfileManagementNotFound.class)
    public ResponseEntity<ErrorResponse> handleAudienceNotFoundException(ProfileManagementNotFound ex) {
        ErrorResponse.ErrorDetail error = ErrorResponse.ErrorDetail.builder()
                .code("PROFILE_MANAGEMENT_NOT_FOUND")
                .message("profile not found")
                .target("id")
                .details(
                        Collections.singletonList(ErrorResponse.ErrorDetail.SubErrorDetail.builder()
                                .code(ex.getCode()).message(ex.getMessage()).build())
                )
                .build();
        return new ResponseEntity<>(new ErrorResponse(error, new Meta()), HttpStatus.NOT_FOUND);
    }
}
