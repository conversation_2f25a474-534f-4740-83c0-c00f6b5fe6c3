package com.vcc.bigdata.presentation.advice;

import com.vcc.bigdata.domain.exception.InvalidParamException;
import com.vcc.bigdata.presentation.response.ErrorResponse;
import com.vcc.bigdata.presentation.response.Meta;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.Collections;


@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(InvalidParamException.class)
    public ResponseEntity<ErrorResponse> handleException(InvalidParamException ex) {
        ErrorResponse.ErrorDetail error = ErrorResponse.ErrorDetail.builder()
                .code("INVALID_PARAM")
                .message("Invalid parameter")
                .details(
                        Collections.singletonList(ErrorResponse.ErrorDetail.SubErrorDetail.builder()
                                .code(ex.getCode()).message(ex.getMessage()).build())
                )
                .build();
        return new ResponseEntity<>(new ErrorResponse(error, new Meta()), HttpStatus.BAD_REQUEST);
    }

    // Optionally catch all other exceptions
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<ErrorResponse> handleGeneric(RuntimeException ex) {
        ErrorResponse.ErrorDetail error = ErrorResponse.ErrorDetail.builder()
                .code("INTERNAL_ERROR")
                .message("An unexpected error occurred.")
                .details(
                        Collections.singletonList(ErrorResponse.ErrorDetail.SubErrorDetail.builder()
                                .code("INTERNAL_ERROR").message(ex.getMessage()).build())
                )
                .build();
        return new ResponseEntity<>(new ErrorResponse(error, new Meta()), HttpStatus.INTERNAL_SERVER_ERROR);
    }
}
