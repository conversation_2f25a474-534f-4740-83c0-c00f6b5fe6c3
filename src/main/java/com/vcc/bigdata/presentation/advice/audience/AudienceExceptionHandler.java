package com.vcc.bigdata.presentation.advice.audience;

import com.vcc.bigdata.domain.exception.audience.AudienceNotFoundException;
import com.vcc.bigdata.presentation.response.ErrorResponse;
import com.vcc.bigdata.presentation.response.Meta;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.Collections;

@RestControllerAdvice
public class AudienceExceptionHandler {
    @ExceptionHandler(AudienceNotFoundException.class)
    public ResponseEntity<ErrorResponse> handleAudienceNotFoundException(AudienceNotFoundException ex) {
        ErrorResponse.ErrorDetail error = ErrorResponse.ErrorDetail.builder()
                .code("AUDIENCE_NOT_FOUND")
                .message("Audience not found")
                .target("id")
                .details(
                        Collections.singletonList(ErrorResponse.ErrorDetail.SubErrorDetail.builder()
                                .code(ex.getCode()).message(ex.getMessage()).build())
                )
                .build();
        return new ResponseEntity<>(new ErrorResponse(error, new Meta()), HttpStatus.NOT_FOUND);
    }
}
