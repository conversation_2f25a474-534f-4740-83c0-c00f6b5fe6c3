package com.vcc.bigdata.presentation.controller.audience;

import com.vcc.bigdata.application.dto.AudienceDto;
import com.vcc.bigdata.application.dto.ProfilesResponseDto;
import com.vcc.bigdata.domain.service.IAudienceService;
import com.vcc.bigdata.presentation.response.Response;
import com.vcc.bigdata.presentation.response.ResponseFactory;
import com.vcc.bigdata.presentation.response.audience.AudienceResponse;
import com.vcc.bigdata.utility.Hashings;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("v1/audiences")
public class AudienceController {
    private final IAudienceService audienceService;
    public AudienceController(IAudienceService audienceService) {
        this.audienceService = audienceService;
    }

    @GetMapping
    AudienceResponse<List<AudienceDto>> getAudiences(@RequestHeader("user-id") String userId,
                                @RequestParam(value = "shared", required = false, defaultValue = "0") Integer shared,
                                @RequestParam(value = "sort", required = false, defaultValue = "created_at:asc") String sort,
                                @RequestParam(value = "search", required = false) String search,
                                @RequestParam(value = "page", required = false, defaultValue = "0") Integer page,
                                @RequestParam(value = "limit", required = false, defaultValue = "10") Integer limit,
                                @RequestParam(value = "from_date", required = false) String fromDate,
                                @RequestParam(value = "to_date", required = false) String toDate,
                                @RequestParam(value = "date_field", required = false) String dateField,
                                @RequestParam(value = "type", required = false, defaultValue = "0") Integer type) {
        if (limit > 100) limit = 100;
        return audienceService.getAll(Hashings.CRC32(userId), shared, sort, page,
                limit, search, fromDate, toDate, dateField, type);
    }

    @GetMapping("/{id}")
    Response getAudience(@PathVariable("id") String id,
                         @RequestHeader("user-id") String userId) {
        AudienceDto audience = audienceService.getAudience(Hashings.CRC32(userId), Long.parseLong(id));
        return ResponseFactory.getSuccessResponse("Get success", audience);
    }

    @PostMapping
    Response createAudience(@RequestBody AudienceDto audienceDto,
                            @RequestHeader("user-id") String userId) {
        audienceDto.setUserId(userId);
        return ResponseFactory.getSuccessResponse("Create success", audienceService.createAudience(audienceDto));
    }

    @PutMapping("/{id}")
    Response updateAudience(@PathVariable("id") String id,
                            @RequestBody AudienceDto audienceDto,
                            @RequestHeader("user-id") String userId) {
        audienceDto.setUserId(userId);
        audienceDto.setId(id);
        return ResponseFactory.getSuccessResponse("Update success", audienceService.updateAudience(audienceDto));
    }

    @DeleteMapping("/{id}")
    Response deleteAudience(@PathVariable("id") String id,
                            @RequestHeader("user-id") String userId) {
        audienceService.deleteAudience(Hashings.CRC32(userId), Long.parseLong(id));
        return ResponseFactory.getSuccessResponse("Delete success", AudienceDto.builder().id(id).build());
    }

    @PostMapping("/estimate")
    Response estimate(@RequestBody AudienceDto audienceDto,
                          @RequestHeader("user-id") String userId) {
        audienceDto.setUserId(Hashings.CRC32(userId).toString());
        return ResponseFactory.getSuccessResponse("Estimate success", audienceService.estimate(audienceDto));
    }

    @PostMapping("/preview-profiles")
    Response previewProfiles(@RequestBody AudienceDto audienceDto,
                          @RequestHeader("user-id") String userId) {
        audienceDto.setUserId(Hashings.CRC32(userId).toString());
        return ResponseFactory.getSuccessResponse("Preview success", audienceService.previewProfiles(audienceDto));
    }

    @GetMapping("/{id}/profiles")
    AudienceResponse<ProfilesResponseDto> detailProfiles(@RequestHeader("user-id") String userId,
                            @PathVariable("id") String id,
                            @RequestParam(value = "page", required = false, defaultValue = "0") Integer page,
                            @RequestParam(value = "limit", required = false, defaultValue = "10") Integer limit,
                            @RequestParam(value = "export", required = false, defaultValue = "false") boolean export) {
        if (limit > 100) limit = 100;
        return audienceService.detailProfiles(page, limit, export, Hashings.CRC32(userId), Long.parseLong(id));
    }

    @GetMapping("check-name")
    Response checkName(@RequestHeader("user-id") String userId,
                       @RequestParam("name") String name) {
        return ResponseFactory.getSuccessResponse("Check segmentation name success", audienceService.checkNameExists(Hashings.CRC32(userId), name));
    }

    @PostMapping("total-profiles")
    Response getTotalProfiles(@RequestHeader("user-id") String userId,
                              @RequestBody List<Long> audienceIds) {
        return ResponseFactory.getSuccessResponse("Get success", audienceService.getTotalProfilesOfAudience(Hashings.CRC32(userId), audienceIds));
    }


}
