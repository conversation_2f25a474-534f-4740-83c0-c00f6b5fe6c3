package com.vcc.bigdata.presentation.response;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class Meta {
    private String timestamp;
    private int page;
    private int limit;
    private int total;

    public Meta(Integer page, Integer limit, int size) {
        this.timestamp = LocalDateTime.now().toString();
        this.page = page;
        this.limit = limit;
        this.total = size;
    }

    public Meta() {
        this.timestamp = LocalDateTime.now().toString();
    }
}
