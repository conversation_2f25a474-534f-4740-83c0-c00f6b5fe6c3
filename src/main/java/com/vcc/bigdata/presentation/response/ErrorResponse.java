package com.vcc.bigdata.presentation.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class ErrorResponse {
    private ErrorDetail error;
    private Meta meta;

    public ErrorResponse(ErrorDetail error, Meta meta) {
        this.error = error;
        this.meta = meta;
    }
    public ErrorResponse(ErrorDetail error) {
        this.error = error;
        this.meta = new Meta();
    }

    @Data
    @Builder
    public static class ErrorDetail {
        private String code;
        private String message;
        private String target;
        private List<SubErrorDetail> details;

        @Data
        @Builder
        public static class SubErrorDetail {
            private String code;
            private String message;
        }
    }
}
